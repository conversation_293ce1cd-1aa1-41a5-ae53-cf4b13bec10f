"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pngjs";
exports.ids = ["vendor-chunks/pngjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/pngjs/lib/bitmapper.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitmapper.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(ssr)/./node_modules/pngjs/lib/interlace.js\");\n\nlet pixelBppMapper = [\n  // 0 - dummy entry\n  function () {},\n\n  // 1 - L\n  // 0: 0, 1: 0, 2: 0, 3: 0xff\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos === data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    let pixel = data[rawPos];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = 0xff;\n  },\n\n  // 2 - LA\n  // 0: 0, 1: 0, 2: 0, 3: 1\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos + 1 >= data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    let pixel = data[rawPos];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = data[rawPos + 1];\n  },\n\n  // 3 - RGB\n  // 0: 0, 1: 1, 2: 2, 3: 0xff\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos + 2 >= data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    pxData[pxPos] = data[rawPos];\n    pxData[pxPos + 1] = data[rawPos + 1];\n    pxData[pxPos + 2] = data[rawPos + 2];\n    pxData[pxPos + 3] = 0xff;\n  },\n\n  // 4 - RGBA\n  // 0: 0, 1: 1, 2: 2, 3: 3\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos + 3 >= data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    pxData[pxPos] = data[rawPos];\n    pxData[pxPos + 1] = data[rawPos + 1];\n    pxData[pxPos + 2] = data[rawPos + 2];\n    pxData[pxPos + 3] = data[rawPos + 3];\n  },\n];\n\nlet pixelBppCustomMapper = [\n  // 0 - dummy entry\n  function () {},\n\n  // 1 - L\n  // 0: 0, 1: 0, 2: 0, 3: 0xff\n  function (pxData, pixelData, pxPos, maxBit) {\n    let pixel = pixelData[0];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = maxBit;\n  },\n\n  // 2 - LA\n  // 0: 0, 1: 0, 2: 0, 3: 1\n  function (pxData, pixelData, pxPos) {\n    let pixel = pixelData[0];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = pixelData[1];\n  },\n\n  // 3 - RGB\n  // 0: 0, 1: 1, 2: 2, 3: 0xff\n  function (pxData, pixelData, pxPos, maxBit) {\n    pxData[pxPos] = pixelData[0];\n    pxData[pxPos + 1] = pixelData[1];\n    pxData[pxPos + 2] = pixelData[2];\n    pxData[pxPos + 3] = maxBit;\n  },\n\n  // 4 - RGBA\n  // 0: 0, 1: 1, 2: 2, 3: 3\n  function (pxData, pixelData, pxPos) {\n    pxData[pxPos] = pixelData[0];\n    pxData[pxPos + 1] = pixelData[1];\n    pxData[pxPos + 2] = pixelData[2];\n    pxData[pxPos + 3] = pixelData[3];\n  },\n];\n\nfunction bitRetriever(data, depth) {\n  let leftOver = [];\n  let i = 0;\n\n  function split() {\n    if (i === data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n    let byte = data[i];\n    i++;\n    let byte8, byte7, byte6, byte5, byte4, byte3, byte2, byte1;\n    switch (depth) {\n      default:\n        throw new Error(\"unrecognised depth\");\n      case 16:\n        byte2 = data[i];\n        i++;\n        leftOver.push((byte << 8) + byte2);\n        break;\n      case 4:\n        byte2 = byte & 0x0f;\n        byte1 = byte >> 4;\n        leftOver.push(byte1, byte2);\n        break;\n      case 2:\n        byte4 = byte & 3;\n        byte3 = (byte >> 2) & 3;\n        byte2 = (byte >> 4) & 3;\n        byte1 = (byte >> 6) & 3;\n        leftOver.push(byte1, byte2, byte3, byte4);\n        break;\n      case 1:\n        byte8 = byte & 1;\n        byte7 = (byte >> 1) & 1;\n        byte6 = (byte >> 2) & 1;\n        byte5 = (byte >> 3) & 1;\n        byte4 = (byte >> 4) & 1;\n        byte3 = (byte >> 5) & 1;\n        byte2 = (byte >> 6) & 1;\n        byte1 = (byte >> 7) & 1;\n        leftOver.push(byte1, byte2, byte3, byte4, byte5, byte6, byte7, byte8);\n        break;\n    }\n  }\n\n  return {\n    get: function (count) {\n      while (leftOver.length < count) {\n        split();\n      }\n      let returner = leftOver.slice(0, count);\n      leftOver = leftOver.slice(count);\n      return returner;\n    },\n    resetAfterLine: function () {\n      leftOver.length = 0;\n    },\n    end: function () {\n      if (i !== data.length) {\n        throw new Error(\"extra data found\");\n      }\n    },\n  };\n}\n\nfunction mapImage8Bit(image, pxData, getPxPos, bpp, data, rawPos) {\n  // eslint-disable-line max-params\n  let imageWidth = image.width;\n  let imageHeight = image.height;\n  let imagePass = image.index;\n  for (let y = 0; y < imageHeight; y++) {\n    for (let x = 0; x < imageWidth; x++) {\n      let pxPos = getPxPos(x, y, imagePass);\n      pixelBppMapper[bpp](pxData, data, pxPos, rawPos);\n      rawPos += bpp; //eslint-disable-line no-param-reassign\n    }\n  }\n  return rawPos;\n}\n\nfunction mapImageCustomBit(image, pxData, getPxPos, bpp, bits, maxBit) {\n  // eslint-disable-line max-params\n  let imageWidth = image.width;\n  let imageHeight = image.height;\n  let imagePass = image.index;\n  for (let y = 0; y < imageHeight; y++) {\n    for (let x = 0; x < imageWidth; x++) {\n      let pixelData = bits.get(bpp);\n      let pxPos = getPxPos(x, y, imagePass);\n      pixelBppCustomMapper[bpp](pxData, pixelData, pxPos, maxBit);\n    }\n    bits.resetAfterLine();\n  }\n}\n\nexports.dataToBitMap = function (data, bitmapInfo) {\n  let width = bitmapInfo.width;\n  let height = bitmapInfo.height;\n  let depth = bitmapInfo.depth;\n  let bpp = bitmapInfo.bpp;\n  let interlace = bitmapInfo.interlace;\n  let bits;\n\n  if (depth !== 8) {\n    bits = bitRetriever(data, depth);\n  }\n  let pxData;\n  if (depth <= 8) {\n    pxData = Buffer.alloc(width * height * 4);\n  } else {\n    pxData = new Uint16Array(width * height * 4);\n  }\n  let maxBit = Math.pow(2, depth) - 1;\n  let rawPos = 0;\n  let images;\n  let getPxPos;\n\n  if (interlace) {\n    images = interlaceUtils.getImagePasses(width, height);\n    getPxPos = interlaceUtils.getInterlaceIterator(width, height);\n  } else {\n    let nonInterlacedPxPos = 0;\n    getPxPos = function () {\n      let returner = nonInterlacedPxPos;\n      nonInterlacedPxPos += 4;\n      return returner;\n    };\n    images = [{ width: width, height: height }];\n  }\n\n  for (let imageIndex = 0; imageIndex < images.length; imageIndex++) {\n    if (depth === 8) {\n      rawPos = mapImage8Bit(\n        images[imageIndex],\n        pxData,\n        getPxPos,\n        bpp,\n        data,\n        rawPos\n      );\n    } else {\n      mapImageCustomBit(\n        images[imageIndex],\n        pxData,\n        getPxPos,\n        bpp,\n        bits,\n        maxBit\n      );\n    }\n  }\n  if (depth === 8) {\n    if (rawPos !== data.length) {\n      throw new Error(\"extra data found\");\n    }\n  } else {\n    bits.end();\n  }\n\n  return pxData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/bitmapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/bitpacker.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitpacker.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\n\nmodule.exports = function (dataIn, width, height, options) {\n  let outHasAlpha =\n    [constants.COLORTYPE_COLOR_ALPHA, constants.COLORTYPE_ALPHA].indexOf(\n      options.colorType\n    ) !== -1;\n  if (options.colorType === options.inputColorType) {\n    let bigEndian = (function () {\n      let buffer = new ArrayBuffer(2);\n      new DataView(buffer).setInt16(0, 256, true /* littleEndian */);\n      // Int16Array uses the platform's endianness.\n      return new Int16Array(buffer)[0] !== 256;\n    })();\n    // If no need to convert to grayscale and alpha is present/absent in both, take a fast route\n    if (options.bitDepth === 8 || (options.bitDepth === 16 && bigEndian)) {\n      return dataIn;\n    }\n  }\n\n  // map to a UInt16 array if data is 16bit, fix endianness below\n  let data = options.bitDepth !== 16 ? dataIn : new Uint16Array(dataIn.buffer);\n\n  let maxValue = 255;\n  let inBpp = constants.COLORTYPE_TO_BPP_MAP[options.inputColorType];\n  if (inBpp === 4 && !options.inputHasAlpha) {\n    inBpp = 3;\n  }\n  let outBpp = constants.COLORTYPE_TO_BPP_MAP[options.colorType];\n  if (options.bitDepth === 16) {\n    maxValue = 65535;\n    outBpp *= 2;\n  }\n  let outData = Buffer.alloc(width * height * outBpp);\n\n  let inIndex = 0;\n  let outIndex = 0;\n\n  let bgColor = options.bgColor || {};\n  if (bgColor.red === undefined) {\n    bgColor.red = maxValue;\n  }\n  if (bgColor.green === undefined) {\n    bgColor.green = maxValue;\n  }\n  if (bgColor.blue === undefined) {\n    bgColor.blue = maxValue;\n  }\n\n  function getRGBA() {\n    let red;\n    let green;\n    let blue;\n    let alpha = maxValue;\n    switch (options.inputColorType) {\n      case constants.COLORTYPE_COLOR_ALPHA:\n        alpha = data[inIndex + 3];\n        red = data[inIndex];\n        green = data[inIndex + 1];\n        blue = data[inIndex + 2];\n        break;\n      case constants.COLORTYPE_COLOR:\n        red = data[inIndex];\n        green = data[inIndex + 1];\n        blue = data[inIndex + 2];\n        break;\n      case constants.COLORTYPE_ALPHA:\n        alpha = data[inIndex + 1];\n        red = data[inIndex];\n        green = red;\n        blue = red;\n        break;\n      case constants.COLORTYPE_GRAYSCALE:\n        red = data[inIndex];\n        green = red;\n        blue = red;\n        break;\n      default:\n        throw new Error(\n          \"input color type:\" +\n            options.inputColorType +\n            \" is not supported at present\"\n        );\n    }\n\n    if (options.inputHasAlpha) {\n      if (!outHasAlpha) {\n        alpha /= maxValue;\n        red = Math.min(\n          Math.max(Math.round((1 - alpha) * bgColor.red + alpha * red), 0),\n          maxValue\n        );\n        green = Math.min(\n          Math.max(Math.round((1 - alpha) * bgColor.green + alpha * green), 0),\n          maxValue\n        );\n        blue = Math.min(\n          Math.max(Math.round((1 - alpha) * bgColor.blue + alpha * blue), 0),\n          maxValue\n        );\n      }\n    }\n    return { red: red, green: green, blue: blue, alpha: alpha };\n  }\n\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let rgba = getRGBA(data, inIndex);\n\n      switch (options.colorType) {\n        case constants.COLORTYPE_COLOR_ALPHA:\n        case constants.COLORTYPE_COLOR:\n          if (options.bitDepth === 8) {\n            outData[outIndex] = rgba.red;\n            outData[outIndex + 1] = rgba.green;\n            outData[outIndex + 2] = rgba.blue;\n            if (outHasAlpha) {\n              outData[outIndex + 3] = rgba.alpha;\n            }\n          } else {\n            outData.writeUInt16BE(rgba.red, outIndex);\n            outData.writeUInt16BE(rgba.green, outIndex + 2);\n            outData.writeUInt16BE(rgba.blue, outIndex + 4);\n            if (outHasAlpha) {\n              outData.writeUInt16BE(rgba.alpha, outIndex + 6);\n            }\n          }\n          break;\n        case constants.COLORTYPE_ALPHA:\n        case constants.COLORTYPE_GRAYSCALE: {\n          // Convert to grayscale and alpha\n          let grayscale = (rgba.red + rgba.green + rgba.blue) / 3;\n          if (options.bitDepth === 8) {\n            outData[outIndex] = grayscale;\n            if (outHasAlpha) {\n              outData[outIndex + 1] = rgba.alpha;\n            }\n          } else {\n            outData.writeUInt16BE(grayscale, outIndex);\n            if (outHasAlpha) {\n              outData.writeUInt16BE(rgba.alpha, outIndex + 2);\n            }\n          }\n          break;\n        }\n        default:\n          throw new Error(\"unrecognised color Type \" + options.colorType);\n      }\n\n      inIndex += inBpp;\n      outIndex += outBpp;\n    }\n  }\n\n  return outData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/bitpacker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/chunkstream.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/chunkstream.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\n\nlet ChunkStream = (module.exports = function () {\n  Stream.call(this);\n\n  this._buffers = [];\n  this._buffered = 0;\n\n  this._reads = [];\n  this._paused = false;\n\n  this._encoding = \"utf8\";\n  this.writable = true;\n});\nutil.inherits(ChunkStream, Stream);\n\nChunkStream.prototype.read = function (length, callback) {\n  this._reads.push({\n    length: Math.abs(length), // if length < 0 then at most this length\n    allowLess: length < 0,\n    func: callback,\n  });\n\n  process.nextTick(\n    function () {\n      this._process();\n\n      // its paused and there is not enought data then ask for more\n      if (this._paused && this._reads && this._reads.length > 0) {\n        this._paused = false;\n\n        this.emit(\"drain\");\n      }\n    }.bind(this)\n  );\n};\n\nChunkStream.prototype.write = function (data, encoding) {\n  if (!this.writable) {\n    this.emit(\"error\", new Error(\"Stream not writable\"));\n    return false;\n  }\n\n  let dataBuffer;\n  if (Buffer.isBuffer(data)) {\n    dataBuffer = data;\n  } else {\n    dataBuffer = Buffer.from(data, encoding || this._encoding);\n  }\n\n  this._buffers.push(dataBuffer);\n  this._buffered += dataBuffer.length;\n\n  this._process();\n\n  // ok if there are no more read requests\n  if (this._reads && this._reads.length === 0) {\n    this._paused = true;\n  }\n\n  return this.writable && !this._paused;\n};\n\nChunkStream.prototype.end = function (data, encoding) {\n  if (data) {\n    this.write(data, encoding);\n  }\n\n  this.writable = false;\n\n  // already destroyed\n  if (!this._buffers) {\n    return;\n  }\n\n  // enqueue or handle end\n  if (this._buffers.length === 0) {\n    this._end();\n  } else {\n    this._buffers.push(null);\n    this._process();\n  }\n};\n\nChunkStream.prototype.destroySoon = ChunkStream.prototype.end;\n\nChunkStream.prototype._end = function () {\n  if (this._reads.length > 0) {\n    this.emit(\"error\", new Error(\"Unexpected end of input\"));\n  }\n\n  this.destroy();\n};\n\nChunkStream.prototype.destroy = function () {\n  if (!this._buffers) {\n    return;\n  }\n\n  this.writable = false;\n  this._reads = null;\n  this._buffers = null;\n\n  this.emit(\"close\");\n};\n\nChunkStream.prototype._processReadAllowingLess = function (read) {\n  // ok there is any data so that we can satisfy this request\n  this._reads.shift(); // == read\n\n  // first we need to peek into first buffer\n  let smallerBuf = this._buffers[0];\n\n  // ok there is more data than we need\n  if (smallerBuf.length > read.length) {\n    this._buffered -= read.length;\n    this._buffers[0] = smallerBuf.slice(read.length);\n\n    read.func.call(this, smallerBuf.slice(0, read.length));\n  } else {\n    // ok this is less than maximum length so use it all\n    this._buffered -= smallerBuf.length;\n    this._buffers.shift(); // == smallerBuf\n\n    read.func.call(this, smallerBuf);\n  }\n};\n\nChunkStream.prototype._processRead = function (read) {\n  this._reads.shift(); // == read\n\n  let pos = 0;\n  let count = 0;\n  let data = Buffer.alloc(read.length);\n\n  // create buffer for all data\n  while (pos < read.length) {\n    let buf = this._buffers[count++];\n    let len = Math.min(buf.length, read.length - pos);\n\n    buf.copy(data, pos, 0, len);\n    pos += len;\n\n    // last buffer wasn't used all so just slice it and leave\n    if (len !== buf.length) {\n      this._buffers[--count] = buf.slice(len);\n    }\n  }\n\n  // remove all used buffers\n  if (count > 0) {\n    this._buffers.splice(0, count);\n  }\n\n  this._buffered -= read.length;\n\n  read.func.call(this, data);\n};\n\nChunkStream.prototype._process = function () {\n  try {\n    // as long as there is any data and read requests\n    while (this._buffered > 0 && this._reads && this._reads.length > 0) {\n      let read = this._reads[0];\n\n      // read any data (but no more than length)\n      if (read.allowLess) {\n        this._processReadAllowingLess(read);\n      } else if (this._buffered >= read.length) {\n        // ok we can meet some expectations\n\n        this._processRead(read);\n      } else {\n        // not enought data to satisfy first request in queue\n        // so we need to wait for more\n        break;\n      }\n    }\n\n    if (this._buffers && !this.writable) {\n      this._end();\n    }\n  } catch (ex) {\n    this.emit(\"error\", ex);\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/chunkstream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/constants.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/constants.js ***!
  \*********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  PNG_SIGNATURE: [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a],\n\n  TYPE_IHDR: 0x49484452,\n  TYPE_IEND: 0x49454e44,\n  TYPE_IDAT: 0x49444154,\n  TYPE_PLTE: 0x504c5445,\n  TYPE_tRNS: 0x74524e53, // eslint-disable-line camelcase\n  TYPE_gAMA: 0x67414d41, // eslint-disable-line camelcase\n\n  // color-type bits\n  COLORTYPE_GRAYSCALE: 0,\n  COLORTYPE_PALETTE: 1,\n  COLORTYPE_COLOR: 2,\n  COLORTYPE_ALPHA: 4, // e.g. grayscale and alpha\n\n  // color-type combinations\n  COLORTYPE_PALETTE_COLOR: 3,\n  COLORTYPE_COLOR_ALPHA: 6,\n\n  COLORTYPE_TO_BPP_MAP: {\n    0: 1,\n    2: 3,\n    3: 1,\n    4: 2,\n    6: 4,\n  },\n\n  GAMMA_DIVISION: 100000,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxidGwtbGFwLXRyaW5oLWRpLWRvbmdcXGFwcHNcXGFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxwbmdqc1xcbGliXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBQTkdfU0lHTkFUVVJFOiBbMHg4OSwgMHg1MCwgMHg0ZSwgMHg0NywgMHgwZCwgMHgwYSwgMHgxYSwgMHgwYV0sXG5cbiAgVFlQRV9JSERSOiAweDQ5NDg0NDUyLFxuICBUWVBFX0lFTkQ6IDB4NDk0NTRlNDQsXG4gIFRZUEVfSURBVDogMHg0OTQ0NDE1NCxcbiAgVFlQRV9QTFRFOiAweDUwNGM1NDQ1LFxuICBUWVBFX3RSTlM6IDB4NzQ1MjRlNTMsIC8vIGVzbGludC1kaXNhYmxlLWxpbmUgY2FtZWxjYXNlXG4gIFRZUEVfZ0FNQTogMHg2NzQxNGQ0MSwgLy8gZXNsaW50LWRpc2FibGUtbGluZSBjYW1lbGNhc2VcblxuICAvLyBjb2xvci10eXBlIGJpdHNcbiAgQ09MT1JUWVBFX0dSQVlTQ0FMRTogMCxcbiAgQ09MT1JUWVBFX1BBTEVUVEU6IDEsXG4gIENPTE9SVFlQRV9DT0xPUjogMixcbiAgQ09MT1JUWVBFX0FMUEhBOiA0LCAvLyBlLmcuIGdyYXlzY2FsZSBhbmQgYWxwaGFcblxuICAvLyBjb2xvci10eXBlIGNvbWJpbmF0aW9uc1xuICBDT0xPUlRZUEVfUEFMRVRURV9DT0xPUjogMyxcbiAgQ09MT1JUWVBFX0NPTE9SX0FMUEhBOiA2LFxuXG4gIENPTE9SVFlQRV9UT19CUFBfTUFQOiB7XG4gICAgMDogMSxcbiAgICAyOiAzLFxuICAgIDM6IDEsXG4gICAgNDogMixcbiAgICA2OiA0LFxuICB9LFxuXG4gIEdBTU1BX0RJVklTSU9OOiAxMDAwMDAsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/crc.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/crc.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nlet crcTable = [];\n\n(function () {\n  for (let i = 0; i < 256; i++) {\n    let currentCrc = i;\n    for (let j = 0; j < 8; j++) {\n      if (currentCrc & 1) {\n        currentCrc = 0xedb88320 ^ (currentCrc >>> 1);\n      } else {\n        currentCrc = currentCrc >>> 1;\n      }\n    }\n    crcTable[i] = currentCrc;\n  }\n})();\n\nlet CrcCalculator = (module.exports = function () {\n  this._crc = -1;\n});\n\nCrcCalculator.prototype.write = function (data) {\n  for (let i = 0; i < data.length; i++) {\n    this._crc = crcTable[(this._crc ^ data[i]) & 0xff] ^ (this._crc >>> 8);\n  }\n  return true;\n};\n\nCrcCalculator.prototype.crc32 = function () {\n  return this._crc ^ -1;\n};\n\nCrcCalculator.crc32 = function (buf) {\n  let crc = -1;\n  for (let i = 0; i < buf.length; i++) {\n    crc = crcTable[(crc ^ buf[i]) & 0xff] ^ (crc >>> 8);\n  }\n  return crc ^ -1;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2NyYy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjs7QUFFQTtBQUNBLGtCQUFrQixTQUFTO0FBQzNCO0FBQ0Esb0JBQW9CLE9BQU87QUFDM0I7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBLGtCQUFrQixpQkFBaUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxrQkFBa0IsZ0JBQWdCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcYnRsLWxhcC10cmluaC1kaS1kb25nXFxhcHBzXFxhZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccG5nanNcXGxpYlxcY3JjLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5sZXQgY3JjVGFibGUgPSBbXTtcblxuKGZ1bmN0aW9uICgpIHtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCAyNTY7IGkrKykge1xuICAgIGxldCBjdXJyZW50Q3JjID0gaTtcbiAgICBmb3IgKGxldCBqID0gMDsgaiA8IDg7IGorKykge1xuICAgICAgaWYgKGN1cnJlbnRDcmMgJiAxKSB7XG4gICAgICAgIGN1cnJlbnRDcmMgPSAweGVkYjg4MzIwIF4gKGN1cnJlbnRDcmMgPj4+IDEpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY3VycmVudENyYyA9IGN1cnJlbnRDcmMgPj4+IDE7XG4gICAgICB9XG4gICAgfVxuICAgIGNyY1RhYmxlW2ldID0gY3VycmVudENyYztcbiAgfVxufSkoKTtcblxubGV0IENyY0NhbGN1bGF0b3IgPSAobW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoKSB7XG4gIHRoaXMuX2NyYyA9IC0xO1xufSk7XG5cbkNyY0NhbGN1bGF0b3IucHJvdG90eXBlLndyaXRlID0gZnVuY3Rpb24gKGRhdGEpIHtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSsrKSB7XG4gICAgdGhpcy5fY3JjID0gY3JjVGFibGVbKHRoaXMuX2NyYyBeIGRhdGFbaV0pICYgMHhmZl0gXiAodGhpcy5fY3JjID4+PiA4KTtcbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn07XG5cbkNyY0NhbGN1bGF0b3IucHJvdG90eXBlLmNyYzMyID0gZnVuY3Rpb24gKCkge1xuICByZXR1cm4gdGhpcy5fY3JjIF4gLTE7XG59O1xuXG5DcmNDYWxjdWxhdG9yLmNyYzMyID0gZnVuY3Rpb24gKGJ1Zikge1xuICBsZXQgY3JjID0gLTE7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgYnVmLmxlbmd0aDsgaSsrKSB7XG4gICAgY3JjID0gY3JjVGFibGVbKGNyYyBeIGJ1ZltpXSkgJiAweGZmXSBeIChjcmMgPj4+IDgpO1xuICB9XG4gIHJldHVybiBjcmMgXiAtMTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/crc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-pack.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/filter-pack.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\");\n\nfunction filterNone(pxData, pxPos, byteWidth, rawData, rawPos) {\n  for (let x = 0; x < byteWidth; x++) {\n    rawData[rawPos + x] = pxData[pxPos + x];\n  }\n}\n\nfunction filterSumNone(pxData, pxPos, byteWidth) {\n  let sum = 0;\n  let length = pxPos + byteWidth;\n\n  for (let i = pxPos; i < length; i++) {\n    sum += Math.abs(pxData[i]);\n  }\n  return sum;\n}\n\nfunction filterSub(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let val = pxData[pxPos + x] - left;\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumSub(pxData, pxPos, byteWidth, bpp) {\n  let sum = 0;\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let val = pxData[pxPos + x] - left;\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterUp(pxData, pxPos, byteWidth, rawData, rawPos) {\n  for (let x = 0; x < byteWidth; x++) {\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let val = pxData[pxPos + x] - up;\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumUp(pxData, pxPos, byteWidth) {\n  let sum = 0;\n  let length = pxPos + byteWidth;\n  for (let x = pxPos; x < length; x++) {\n    let up = pxPos > 0 ? pxData[x - byteWidth] : 0;\n    let val = pxData[x] - up;\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterAvg(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let val = pxData[pxPos + x] - ((left + up) >> 1);\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumAvg(pxData, pxPos, byteWidth, bpp) {\n  let sum = 0;\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let val = pxData[pxPos + x] - ((left + up) >> 1);\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterPaeth(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let upleft =\n      pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n    let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumPaeth(pxData, pxPos, byteWidth, bpp) {\n  let sum = 0;\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let upleft =\n      pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n    let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nlet filters = {\n  0: filterNone,\n  1: filterSub,\n  2: filterUp,\n  3: filterAvg,\n  4: filterPaeth,\n};\n\nlet filterSums = {\n  0: filterSumNone,\n  1: filterSumSub,\n  2: filterSumUp,\n  3: filterSumAvg,\n  4: filterSumPaeth,\n};\n\nmodule.exports = function (pxData, width, height, options, bpp) {\n  let filterTypes;\n  if (!(\"filterType\" in options) || options.filterType === -1) {\n    filterTypes = [0, 1, 2, 3, 4];\n  } else if (typeof options.filterType === \"number\") {\n    filterTypes = [options.filterType];\n  } else {\n    throw new Error(\"unrecognised filter types\");\n  }\n\n  if (options.bitDepth === 16) {\n    bpp *= 2;\n  }\n  let byteWidth = width * bpp;\n  let rawPos = 0;\n  let pxPos = 0;\n  let rawData = Buffer.alloc((byteWidth + 1) * height);\n\n  let sel = filterTypes[0];\n\n  for (let y = 0; y < height; y++) {\n    if (filterTypes.length > 1) {\n      // find best filter for this line (with lowest sum of values)\n      let min = Infinity;\n\n      for (let i = 0; i < filterTypes.length; i++) {\n        let sum = filterSums[filterTypes[i]](pxData, pxPos, byteWidth, bpp);\n        if (sum < min) {\n          sel = filterTypes[i];\n          min = sum;\n        }\n      }\n    }\n\n    rawData[rawPos] = sel;\n    rawPos++;\n    filters[sel](pxData, pxPos, byteWidth, rawData, rawPos, bpp);\n    rawPos += byteWidth;\n    pxPos += byteWidth;\n  }\n  return rawData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-pack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse-async.js":
/*!******************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-async.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(ssr)/./node_modules/pngjs/lib/chunkstream.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(ssr)/./node_modules/pngjs/lib/filter-parse.js\");\n\nlet FilterAsync = (module.exports = function (bitmapInfo) {\n  ChunkStream.call(this);\n\n  let buffers = [];\n  let that = this;\n  this._filter = new Filter(bitmapInfo, {\n    read: this.read.bind(this),\n    write: function (buffer) {\n      buffers.push(buffer);\n    },\n    complete: function () {\n      that.emit(\"complete\", Buffer.concat(buffers));\n    },\n  });\n\n  this._filter.start();\n});\nutil.inherits(FilterAsync, ChunkStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS1hc3luYy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixXQUFXLG1CQUFPLENBQUMsa0JBQU07QUFDekIsa0JBQWtCLG1CQUFPLENBQUMsb0VBQWU7QUFDekMsYUFBYSxtQkFBTyxDQUFDLHNFQUFnQjs7QUFFckM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHOztBQUVIO0FBQ0EsQ0FBQztBQUNEIiwic291cmNlcyI6WyJEOlxcYnRsLWxhcC10cmluaC1kaS1kb25nXFxhcHBzXFxhZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccG5nanNcXGxpYlxcZmlsdGVyLXBhcnNlLWFzeW5jLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5sZXQgdXRpbCA9IHJlcXVpcmUoXCJ1dGlsXCIpO1xubGV0IENodW5rU3RyZWFtID0gcmVxdWlyZShcIi4vY2h1bmtzdHJlYW1cIik7XG5sZXQgRmlsdGVyID0gcmVxdWlyZShcIi4vZmlsdGVyLXBhcnNlXCIpO1xuXG5sZXQgRmlsdGVyQXN5bmMgPSAobW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoYml0bWFwSW5mbykge1xuICBDaHVua1N0cmVhbS5jYWxsKHRoaXMpO1xuXG4gIGxldCBidWZmZXJzID0gW107XG4gIGxldCB0aGF0ID0gdGhpcztcbiAgdGhpcy5fZmlsdGVyID0gbmV3IEZpbHRlcihiaXRtYXBJbmZvLCB7XG4gICAgcmVhZDogdGhpcy5yZWFkLmJpbmQodGhpcyksXG4gICAgd3JpdGU6IGZ1bmN0aW9uIChidWZmZXIpIHtcbiAgICAgIGJ1ZmZlcnMucHVzaChidWZmZXIpO1xuICAgIH0sXG4gICAgY29tcGxldGU6IGZ1bmN0aW9uICgpIHtcbiAgICAgIHRoYXQuZW1pdChcImNvbXBsZXRlXCIsIEJ1ZmZlci5jb25jYXQoYnVmZmVycykpO1xuICAgIH0sXG4gIH0pO1xuXG4gIHRoaXMuX2ZpbHRlci5zdGFydCgpO1xufSk7XG51dGlsLmluaGVyaXRzKEZpbHRlckFzeW5jLCBDaHVua1N0cmVhbSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-sync.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(ssr)/./node_modules/pngjs/lib/sync-reader.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(ssr)/./node_modules/pngjs/lib/filter-parse.js\");\n\nexports.process = function (inBuffer, bitmapInfo) {\n  let outBuffers = [];\n  let reader = new SyncReader(inBuffer);\n  let filter = new Filter(bitmapInfo, {\n    read: reader.read.bind(reader),\n    write: function (bufferPart) {\n      outBuffers.push(bufferPart);\n    },\n    complete: function () {},\n  });\n\n  filter.start();\n  reader.process();\n\n  return Buffer.concat(outBuffers);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGlCQUFpQixtQkFBTyxDQUFDLG9FQUFlO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyxzRUFBZ0I7O0FBRXJDLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsNEJBQTRCO0FBQzVCLEdBQUc7O0FBRUg7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJEOlxcYnRsLWxhcC10cmluaC1kaS1kb25nXFxhcHBzXFxhZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccG5nanNcXGxpYlxcZmlsdGVyLXBhcnNlLXN5bmMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmxldCBTeW5jUmVhZGVyID0gcmVxdWlyZShcIi4vc3luYy1yZWFkZXJcIik7XG5sZXQgRmlsdGVyID0gcmVxdWlyZShcIi4vZmlsdGVyLXBhcnNlXCIpO1xuXG5leHBvcnRzLnByb2Nlc3MgPSBmdW5jdGlvbiAoaW5CdWZmZXIsIGJpdG1hcEluZm8pIHtcbiAgbGV0IG91dEJ1ZmZlcnMgPSBbXTtcbiAgbGV0IHJlYWRlciA9IG5ldyBTeW5jUmVhZGVyKGluQnVmZmVyKTtcbiAgbGV0IGZpbHRlciA9IG5ldyBGaWx0ZXIoYml0bWFwSW5mbywge1xuICAgIHJlYWQ6IHJlYWRlci5yZWFkLmJpbmQocmVhZGVyKSxcbiAgICB3cml0ZTogZnVuY3Rpb24gKGJ1ZmZlclBhcnQpIHtcbiAgICAgIG91dEJ1ZmZlcnMucHVzaChidWZmZXJQYXJ0KTtcbiAgICB9LFxuICAgIGNvbXBsZXRlOiBmdW5jdGlvbiAoKSB7fSxcbiAgfSk7XG5cbiAgZmlsdGVyLnN0YXJ0KCk7XG4gIHJlYWRlci5wcm9jZXNzKCk7XG5cbiAgcmV0dXJuIEJ1ZmZlci5jb25jYXQob3V0QnVmZmVycyk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(ssr)/./node_modules/pngjs/lib/interlace.js\");\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\");\n\nfunction getByteWidth(width, bpp, depth) {\n  let byteWidth = width * bpp;\n  if (depth !== 8) {\n    byteWidth = Math.ceil(byteWidth / (8 / depth));\n  }\n  return byteWidth;\n}\n\nlet Filter = (module.exports = function (bitmapInfo, dependencies) {\n  let width = bitmapInfo.width;\n  let height = bitmapInfo.height;\n  let interlace = bitmapInfo.interlace;\n  let bpp = bitmapInfo.bpp;\n  let depth = bitmapInfo.depth;\n\n  this.read = dependencies.read;\n  this.write = dependencies.write;\n  this.complete = dependencies.complete;\n\n  this._imageIndex = 0;\n  this._images = [];\n  if (interlace) {\n    let passes = interlaceUtils.getImagePasses(width, height);\n    for (let i = 0; i < passes.length; i++) {\n      this._images.push({\n        byteWidth: getByteWidth(passes[i].width, bpp, depth),\n        height: passes[i].height,\n        lineIndex: 0,\n      });\n    }\n  } else {\n    this._images.push({\n      byteWidth: getByteWidth(width, bpp, depth),\n      height: height,\n      lineIndex: 0,\n    });\n  }\n\n  // when filtering the line we look at the pixel to the left\n  // the spec also says it is done on a byte level regardless of the number of pixels\n  // so if the depth is byte compatible (8 or 16) we subtract the bpp in order to compare back\n  // a pixel rather than just a different byte part. However if we are sub byte, we ignore.\n  if (depth === 8) {\n    this._xComparison = bpp;\n  } else if (depth === 16) {\n    this._xComparison = bpp * 2;\n  } else {\n    this._xComparison = 1;\n  }\n});\n\nFilter.prototype.start = function () {\n  this.read(\n    this._images[this._imageIndex].byteWidth + 1,\n    this._reverseFilterLine.bind(this)\n  );\n};\n\nFilter.prototype._unFilterType1 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let xComparison = this._xComparison;\n  let xBiggerThan = xComparison - 1;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f1Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    unfilteredLine[x] = rawByte + f1Left;\n  }\n};\n\nFilter.prototype._unFilterType2 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let lastLine = this._lastLine;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f2Up = lastLine ? lastLine[x] : 0;\n    unfilteredLine[x] = rawByte + f2Up;\n  }\n};\n\nFilter.prototype._unFilterType3 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let xComparison = this._xComparison;\n  let xBiggerThan = xComparison - 1;\n  let lastLine = this._lastLine;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f3Up = lastLine ? lastLine[x] : 0;\n    let f3Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    let f3Add = Math.floor((f3Left + f3Up) / 2);\n    unfilteredLine[x] = rawByte + f3Add;\n  }\n};\n\nFilter.prototype._unFilterType4 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let xComparison = this._xComparison;\n  let xBiggerThan = xComparison - 1;\n  let lastLine = this._lastLine;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f4Up = lastLine ? lastLine[x] : 0;\n    let f4Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    let f4UpLeft = x > xBiggerThan && lastLine ? lastLine[x - xComparison] : 0;\n    let f4Add = paethPredictor(f4Left, f4Up, f4UpLeft);\n    unfilteredLine[x] = rawByte + f4Add;\n  }\n};\n\nFilter.prototype._reverseFilterLine = function (rawData) {\n  let filter = rawData[0];\n  let unfilteredLine;\n  let currentImage = this._images[this._imageIndex];\n  let byteWidth = currentImage.byteWidth;\n\n  if (filter === 0) {\n    unfilteredLine = rawData.slice(1, byteWidth + 1);\n  } else {\n    unfilteredLine = Buffer.alloc(byteWidth);\n\n    switch (filter) {\n      case 1:\n        this._unFilterType1(rawData, unfilteredLine, byteWidth);\n        break;\n      case 2:\n        this._unFilterType2(rawData, unfilteredLine, byteWidth);\n        break;\n      case 3:\n        this._unFilterType3(rawData, unfilteredLine, byteWidth);\n        break;\n      case 4:\n        this._unFilterType4(rawData, unfilteredLine, byteWidth);\n        break;\n      default:\n        throw new Error(\"Unrecognised filter type - \" + filter);\n    }\n  }\n\n  this.write(unfilteredLine);\n\n  currentImage.lineIndex++;\n  if (currentImage.lineIndex >= currentImage.height) {\n    this._lastLine = null;\n    this._imageIndex++;\n    currentImage = this._images[this._imageIndex];\n  } else {\n    this._lastLine = unfilteredLine;\n  }\n\n  if (currentImage) {\n    // read, using the byte width that may be from the new current image\n    this.read(currentImage.byteWidth + 1, this._reverseFilterLine.bind(this));\n  } else {\n    this._lastLine = null;\n    this.complete();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/format-normaliser.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/format-normaliser.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("\n\nfunction dePalette(indata, outdata, width, height, palette) {\n  let pxPos = 0;\n  // use values from palette\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let color = palette[indata[pxPos]];\n\n      if (!color) {\n        throw new Error(\"index \" + indata[pxPos] + \" not in palette\");\n      }\n\n      for (let i = 0; i < 4; i++) {\n        outdata[pxPos + i] = color[i];\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nfunction replaceTransparentColor(indata, outdata, width, height, transColor) {\n  let pxPos = 0;\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let makeTrans = false;\n\n      if (transColor.length === 1) {\n        if (transColor[0] === indata[pxPos]) {\n          makeTrans = true;\n        }\n      } else if (\n        transColor[0] === indata[pxPos] &&\n        transColor[1] === indata[pxPos + 1] &&\n        transColor[2] === indata[pxPos + 2]\n      ) {\n        makeTrans = true;\n      }\n      if (makeTrans) {\n        for (let i = 0; i < 4; i++) {\n          outdata[pxPos + i] = 0;\n        }\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nfunction scaleDepth(indata, outdata, width, height, depth) {\n  let maxOutSample = 255;\n  let maxInSample = Math.pow(2, depth) - 1;\n  let pxPos = 0;\n\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      for (let i = 0; i < 4; i++) {\n        outdata[pxPos + i] = Math.floor(\n          (indata[pxPos + i] * maxOutSample) / maxInSample + 0.5\n        );\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nmodule.exports = function (indata, imageData) {\n  let depth = imageData.depth;\n  let width = imageData.width;\n  let height = imageData.height;\n  let colorType = imageData.colorType;\n  let transColor = imageData.transColor;\n  let palette = imageData.palette;\n\n  let outdata = indata; // only different for 16 bits\n\n  if (colorType === 3) {\n    // paletted\n    dePalette(indata, outdata, width, height, palette);\n  } else {\n    if (transColor) {\n      replaceTransparentColor(indata, outdata, width, height, transColor);\n    }\n    // if it needs scaling\n    if (depth !== 8) {\n      // if we need to change the buffer size\n      if (depth === 16) {\n        outdata = Buffer.alloc(width * height * 4);\n      }\n      scaleDepth(indata, outdata, width, height, depth);\n    }\n  }\n  return outdata;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2Zvcm1hdC1ub3JtYWxpc2VyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixZQUFZO0FBQzlCLG9CQUFvQixXQUFXO0FBQy9COztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxzQkFBc0IsT0FBTztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGtCQUFrQixZQUFZO0FBQzlCLG9CQUFvQixXQUFXO0FBQy9COztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU87QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsa0JBQWtCLFlBQVk7QUFDOUIsb0JBQW9CLFdBQVc7QUFDL0Isc0JBQXNCLE9BQU87QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx3QkFBd0I7O0FBRXhCO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcYnRsLWxhcC10cmluaC1kaS1kb25nXFxhcHBzXFxhZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccG5nanNcXGxpYlxcZm9ybWF0LW5vcm1hbGlzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmZ1bmN0aW9uIGRlUGFsZXR0ZShpbmRhdGEsIG91dGRhdGEsIHdpZHRoLCBoZWlnaHQsIHBhbGV0dGUpIHtcbiAgbGV0IHB4UG9zID0gMDtcbiAgLy8gdXNlIHZhbHVlcyBmcm9tIHBhbGV0dGVcbiAgZm9yIChsZXQgeSA9IDA7IHkgPCBoZWlnaHQ7IHkrKykge1xuICAgIGZvciAobGV0IHggPSAwOyB4IDwgd2lkdGg7IHgrKykge1xuICAgICAgbGV0IGNvbG9yID0gcGFsZXR0ZVtpbmRhdGFbcHhQb3NdXTtcblxuICAgICAgaWYgKCFjb2xvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJpbmRleCBcIiArIGluZGF0YVtweFBvc10gKyBcIiBub3QgaW4gcGFsZXR0ZVwiKTtcbiAgICAgIH1cblxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCA0OyBpKyspIHtcbiAgICAgICAgb3V0ZGF0YVtweFBvcyArIGldID0gY29sb3JbaV07XG4gICAgICB9XG4gICAgICBweFBvcyArPSA0O1xuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiByZXBsYWNlVHJhbnNwYXJlbnRDb2xvcihpbmRhdGEsIG91dGRhdGEsIHdpZHRoLCBoZWlnaHQsIHRyYW5zQ29sb3IpIHtcbiAgbGV0IHB4UG9zID0gMDtcbiAgZm9yIChsZXQgeSA9IDA7IHkgPCBoZWlnaHQ7IHkrKykge1xuICAgIGZvciAobGV0IHggPSAwOyB4IDwgd2lkdGg7IHgrKykge1xuICAgICAgbGV0IG1ha2VUcmFucyA9IGZhbHNlO1xuXG4gICAgICBpZiAodHJhbnNDb2xvci5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgaWYgKHRyYW5zQ29sb3JbMF0gPT09IGluZGF0YVtweFBvc10pIHtcbiAgICAgICAgICBtYWtlVHJhbnMgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKFxuICAgICAgICB0cmFuc0NvbG9yWzBdID09PSBpbmRhdGFbcHhQb3NdICYmXG4gICAgICAgIHRyYW5zQ29sb3JbMV0gPT09IGluZGF0YVtweFBvcyArIDFdICYmXG4gICAgICAgIHRyYW5zQ29sb3JbMl0gPT09IGluZGF0YVtweFBvcyArIDJdXG4gICAgICApIHtcbiAgICAgICAgbWFrZVRyYW5zID0gdHJ1ZTtcbiAgICAgIH1cbiAgICAgIGlmIChtYWtlVHJhbnMpIHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCA0OyBpKyspIHtcbiAgICAgICAgICBvdXRkYXRhW3B4UG9zICsgaV0gPSAwO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBweFBvcyArPSA0O1xuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiBzY2FsZURlcHRoKGluZGF0YSwgb3V0ZGF0YSwgd2lkdGgsIGhlaWdodCwgZGVwdGgpIHtcbiAgbGV0IG1heE91dFNhbXBsZSA9IDI1NTtcbiAgbGV0IG1heEluU2FtcGxlID0gTWF0aC5wb3coMiwgZGVwdGgpIC0gMTtcbiAgbGV0IHB4UG9zID0gMDtcblxuICBmb3IgKGxldCB5ID0gMDsgeSA8IGhlaWdodDsgeSsrKSB7XG4gICAgZm9yIChsZXQgeCA9IDA7IHggPCB3aWR0aDsgeCsrKSB7XG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IDQ7IGkrKykge1xuICAgICAgICBvdXRkYXRhW3B4UG9zICsgaV0gPSBNYXRoLmZsb29yKFxuICAgICAgICAgIChpbmRhdGFbcHhQb3MgKyBpXSAqIG1heE91dFNhbXBsZSkgLyBtYXhJblNhbXBsZSArIDAuNVxuICAgICAgICApO1xuICAgICAgfVxuICAgICAgcHhQb3MgKz0gNDtcbiAgICB9XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoaW5kYXRhLCBpbWFnZURhdGEpIHtcbiAgbGV0IGRlcHRoID0gaW1hZ2VEYXRhLmRlcHRoO1xuICBsZXQgd2lkdGggPSBpbWFnZURhdGEud2lkdGg7XG4gIGxldCBoZWlnaHQgPSBpbWFnZURhdGEuaGVpZ2h0O1xuICBsZXQgY29sb3JUeXBlID0gaW1hZ2VEYXRhLmNvbG9yVHlwZTtcbiAgbGV0IHRyYW5zQ29sb3IgPSBpbWFnZURhdGEudHJhbnNDb2xvcjtcbiAgbGV0IHBhbGV0dGUgPSBpbWFnZURhdGEucGFsZXR0ZTtcblxuICBsZXQgb3V0ZGF0YSA9IGluZGF0YTsgLy8gb25seSBkaWZmZXJlbnQgZm9yIDE2IGJpdHNcblxuICBpZiAoY29sb3JUeXBlID09PSAzKSB7XG4gICAgLy8gcGFsZXR0ZWRcbiAgICBkZVBhbGV0dGUoaW5kYXRhLCBvdXRkYXRhLCB3aWR0aCwgaGVpZ2h0LCBwYWxldHRlKTtcbiAgfSBlbHNlIHtcbiAgICBpZiAodHJhbnNDb2xvcikge1xuICAgICAgcmVwbGFjZVRyYW5zcGFyZW50Q29sb3IoaW5kYXRhLCBvdXRkYXRhLCB3aWR0aCwgaGVpZ2h0LCB0cmFuc0NvbG9yKTtcbiAgICB9XG4gICAgLy8gaWYgaXQgbmVlZHMgc2NhbGluZ1xuICAgIGlmIChkZXB0aCAhPT0gOCkge1xuICAgICAgLy8gaWYgd2UgbmVlZCB0byBjaGFuZ2UgdGhlIGJ1ZmZlciBzaXplXG4gICAgICBpZiAoZGVwdGggPT09IDE2KSB7XG4gICAgICAgIG91dGRhdGEgPSBCdWZmZXIuYWxsb2Mod2lkdGggKiBoZWlnaHQgKiA0KTtcbiAgICAgIH1cbiAgICAgIHNjYWxlRGVwdGgoaW5kYXRhLCBvdXRkYXRhLCB3aWR0aCwgaGVpZ2h0LCBkZXB0aCk7XG4gICAgfVxuICB9XG4gIHJldHVybiBvdXRkYXRhO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/format-normaliser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/interlace.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/interlace.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n// Adam 7\n//   0 1 2 3 4 5 6 7\n// 0 x 6 4 6 x 6 4 6\n// 1 7 7 7 7 7 7 7 7\n// 2 5 6 5 6 5 6 5 6\n// 3 7 7 7 7 7 7 7 7\n// 4 3 6 4 6 3 6 4 6\n// 5 7 7 7 7 7 7 7 7\n// 6 5 6 5 6 5 6 5 6\n// 7 7 7 7 7 7 7 7 7\n\nlet imagePasses = [\n  {\n    // pass 1 - 1px\n    x: [0],\n    y: [0],\n  },\n  {\n    // pass 2 - 1px\n    x: [4],\n    y: [0],\n  },\n  {\n    // pass 3 - 2px\n    x: [0, 4],\n    y: [4],\n  },\n  {\n    // pass 4 - 4px\n    x: [2, 6],\n    y: [0, 4],\n  },\n  {\n    // pass 5 - 8px\n    x: [0, 2, 4, 6],\n    y: [2, 6],\n  },\n  {\n    // pass 6 - 16px\n    x: [1, 3, 5, 7],\n    y: [0, 2, 4, 6],\n  },\n  {\n    // pass 7 - 32px\n    x: [0, 1, 2, 3, 4, 5, 6, 7],\n    y: [1, 3, 5, 7],\n  },\n];\n\nexports.getImagePasses = function (width, height) {\n  let images = [];\n  let xLeftOver = width % 8;\n  let yLeftOver = height % 8;\n  let xRepeats = (width - xLeftOver) / 8;\n  let yRepeats = (height - yLeftOver) / 8;\n  for (let i = 0; i < imagePasses.length; i++) {\n    let pass = imagePasses[i];\n    let passWidth = xRepeats * pass.x.length;\n    let passHeight = yRepeats * pass.y.length;\n    for (let j = 0; j < pass.x.length; j++) {\n      if (pass.x[j] < xLeftOver) {\n        passWidth++;\n      } else {\n        break;\n      }\n    }\n    for (let j = 0; j < pass.y.length; j++) {\n      if (pass.y[j] < yLeftOver) {\n        passHeight++;\n      } else {\n        break;\n      }\n    }\n    if (passWidth > 0 && passHeight > 0) {\n      images.push({ width: passWidth, height: passHeight, index: i });\n    }\n  }\n  return images;\n};\n\nexports.getInterlaceIterator = function (width) {\n  return function (x, y, pass) {\n    let outerXLeftOver = x % imagePasses[pass].x.length;\n    let outerX =\n      ((x - outerXLeftOver) / imagePasses[pass].x.length) * 8 +\n      imagePasses[pass].x[outerXLeftOver];\n    let outerYLeftOver = y % imagePasses[pass].y.length;\n    let outerY =\n      ((y - outerYLeftOver) / imagePasses[pass].y.length) * 8 +\n      imagePasses[pass].y[outerYLeftOver];\n    return outerX * 4 + outerY * width * 4;\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/interlace.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/packer-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(ssr)/./node_modules/pngjs/lib/packer.js\");\n\nlet PackerAsync = (module.exports = function (opt) {\n  Stream.call(this);\n\n  let options = opt || {};\n\n  this._packer = new Packer(options);\n  this._deflate = this._packer.createDeflate();\n\n  this.readable = true;\n});\nutil.inherits(PackerAsync, Stream);\n\nPackerAsync.prototype.pack = function (data, width, height, gamma) {\n  // Signature\n  this.emit(\"data\", Buffer.from(constants.PNG_SIGNATURE));\n  this.emit(\"data\", this._packer.packIHDR(width, height));\n\n  if (gamma) {\n    this.emit(\"data\", this._packer.packGAMA(gamma));\n  }\n\n  let filteredData = this._packer.filterData(data, width, height);\n\n  // compress it\n  this._deflate.on(\"error\", this.emit.bind(this, \"error\"));\n\n  this._deflate.on(\n    \"data\",\n    function (compressedData) {\n      this.emit(\"data\", this._packer.packIDAT(compressedData));\n    }.bind(this)\n  );\n\n  this._deflate.on(\n    \"end\",\n    function () {\n      this.emit(\"data\", this._packer.packIEND());\n      this.emit(\"end\");\n    }.bind(this)\n  );\n\n  this._deflate.end(filteredData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/packer-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nif (!zlib.deflateSync) {\n  hasSyncZlib = false;\n}\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(ssr)/./node_modules/pngjs/lib/packer.js\");\n\nmodule.exports = function (metaData, opt) {\n  if (!hasSyncZlib) {\n    throw new Error(\n      \"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\"\n    );\n  }\n\n  let options = opt || {};\n\n  let packer = new Packer(options);\n\n  let chunks = [];\n\n  // Signature\n  chunks.push(Buffer.from(constants.PNG_SIGNATURE));\n\n  // Header\n  chunks.push(packer.packIHDR(metaData.width, metaData.height));\n\n  if (metaData.gamma) {\n    chunks.push(packer.packGAMA(metaData.gamma));\n  }\n\n  let filteredData = packer.filterData(\n    metaData.data,\n    metaData.width,\n    metaData.height\n  );\n\n  // compress it\n  let compressedData = zlib.deflateSync(\n    filteredData,\n    packer.getDeflateOptions()\n  );\n  filteredData = null;\n\n  if (!compressedData || !compressedData.length) {\n    throw new Error(\"bad png - invalid compressed data response\");\n  }\n  chunks.push(packer.packIDAT(compressedData));\n\n  // End\n  chunks.push(packer.packIEND());\n\n  return Buffer.concat(chunks);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/packer.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet CrcStream = __webpack_require__(/*! ./crc */ \"(ssr)/./node_modules/pngjs/lib/crc.js\");\nlet bitPacker = __webpack_require__(/*! ./bitpacker */ \"(ssr)/./node_modules/pngjs/lib/bitpacker.js\");\nlet filter = __webpack_require__(/*! ./filter-pack */ \"(ssr)/./node_modules/pngjs/lib/filter-pack.js\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\n\nlet Packer = (module.exports = function (options) {\n  this._options = options;\n\n  options.deflateChunkSize = options.deflateChunkSize || 32 * 1024;\n  options.deflateLevel =\n    options.deflateLevel != null ? options.deflateLevel : 9;\n  options.deflateStrategy =\n    options.deflateStrategy != null ? options.deflateStrategy : 3;\n  options.inputHasAlpha =\n    options.inputHasAlpha != null ? options.inputHasAlpha : true;\n  options.deflateFactory = options.deflateFactory || zlib.createDeflate;\n  options.bitDepth = options.bitDepth || 8;\n  // This is outputColorType\n  options.colorType =\n    typeof options.colorType === \"number\"\n      ? options.colorType\n      : constants.COLORTYPE_COLOR_ALPHA;\n  options.inputColorType =\n    typeof options.inputColorType === \"number\"\n      ? options.inputColorType\n      : constants.COLORTYPE_COLOR_ALPHA;\n\n  if (\n    [\n      constants.COLORTYPE_GRAYSCALE,\n      constants.COLORTYPE_COLOR,\n      constants.COLORTYPE_COLOR_ALPHA,\n      constants.COLORTYPE_ALPHA,\n    ].indexOf(options.colorType) === -1\n  ) {\n    throw new Error(\n      \"option color type:\" + options.colorType + \" is not supported at present\"\n    );\n  }\n  if (\n    [\n      constants.COLORTYPE_GRAYSCALE,\n      constants.COLORTYPE_COLOR,\n      constants.COLORTYPE_COLOR_ALPHA,\n      constants.COLORTYPE_ALPHA,\n    ].indexOf(options.inputColorType) === -1\n  ) {\n    throw new Error(\n      \"option input color type:\" +\n        options.inputColorType +\n        \" is not supported at present\"\n    );\n  }\n  if (options.bitDepth !== 8 && options.bitDepth !== 16) {\n    throw new Error(\n      \"option bit depth:\" + options.bitDepth + \" is not supported at present\"\n    );\n  }\n});\n\nPacker.prototype.getDeflateOptions = function () {\n  return {\n    chunkSize: this._options.deflateChunkSize,\n    level: this._options.deflateLevel,\n    strategy: this._options.deflateStrategy,\n  };\n};\n\nPacker.prototype.createDeflate = function () {\n  return this._options.deflateFactory(this.getDeflateOptions());\n};\n\nPacker.prototype.filterData = function (data, width, height) {\n  // convert to correct format for filtering (e.g. right bpp and bit depth)\n  let packedData = bitPacker(data, width, height, this._options);\n\n  // filter pixel data\n  let bpp = constants.COLORTYPE_TO_BPP_MAP[this._options.colorType];\n  let filteredData = filter(packedData, width, height, this._options, bpp);\n  return filteredData;\n};\n\nPacker.prototype._packChunk = function (type, data) {\n  let len = data ? data.length : 0;\n  let buf = Buffer.alloc(len + 12);\n\n  buf.writeUInt32BE(len, 0);\n  buf.writeUInt32BE(type, 4);\n\n  if (data) {\n    data.copy(buf, 8);\n  }\n\n  buf.writeInt32BE(\n    CrcStream.crc32(buf.slice(4, buf.length - 4)),\n    buf.length - 4\n  );\n  return buf;\n};\n\nPacker.prototype.packGAMA = function (gamma) {\n  let buf = Buffer.alloc(4);\n  buf.writeUInt32BE(Math.floor(gamma * constants.GAMMA_DIVISION), 0);\n  return this._packChunk(constants.TYPE_gAMA, buf);\n};\n\nPacker.prototype.packIHDR = function (width, height) {\n  let buf = Buffer.alloc(13);\n  buf.writeUInt32BE(width, 0);\n  buf.writeUInt32BE(height, 4);\n  buf[8] = this._options.bitDepth; // Bit depth\n  buf[9] = this._options.colorType; // colorType\n  buf[10] = 0; // compression\n  buf[11] = 0; // filter\n  buf[12] = 0; // interlace\n\n  return this._packChunk(constants.TYPE_IHDR, buf);\n};\n\nPacker.prototype.packIDAT = function (data) {\n  return this._packChunk(constants.TYPE_IDAT, data);\n};\n\nPacker.prototype.packIEND = function () {\n  return this._packChunk(constants.TYPE_IEND, null);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/paeth-predictor.js":
/*!***************************************************!*\
  !*** ./node_modules/pngjs/lib/paeth-predictor.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = function paethPredictor(left, above, upLeft) {\n  let paeth = left + above - upLeft;\n  let pLeft = Math.abs(paeth - left);\n  let pAbove = Math.abs(paeth - above);\n  let pUpLeft = Math.abs(paeth - upLeft);\n\n  if (pLeft <= pAbove && pLeft <= pUpLeft) {\n    return left;\n  }\n  if (pAbove <= pUpLeft) {\n    return above;\n  }\n  return upLeft;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BhZXRoLXByZWRpY3Rvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxidGwtbGFwLXRyaW5oLWRpLWRvbmdcXGFwcHNcXGFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxwbmdqc1xcbGliXFxwYWV0aC1wcmVkaWN0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gcGFldGhQcmVkaWN0b3IobGVmdCwgYWJvdmUsIHVwTGVmdCkge1xuICBsZXQgcGFldGggPSBsZWZ0ICsgYWJvdmUgLSB1cExlZnQ7XG4gIGxldCBwTGVmdCA9IE1hdGguYWJzKHBhZXRoIC0gbGVmdCk7XG4gIGxldCBwQWJvdmUgPSBNYXRoLmFicyhwYWV0aCAtIGFib3ZlKTtcbiAgbGV0IHBVcExlZnQgPSBNYXRoLmFicyhwYWV0aCAtIHVwTGVmdCk7XG5cbiAgaWYgKHBMZWZ0IDw9IHBBYm92ZSAmJiBwTGVmdCA8PSBwVXBMZWZ0KSB7XG4gICAgcmV0dXJuIGxlZnQ7XG4gIH1cbiAgaWYgKHBBYm92ZSA8PSBwVXBMZWZ0KSB7XG4gICAgcmV0dXJuIGFib3ZlO1xuICB9XG4gIHJldHVybiB1cExlZnQ7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/parser-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(ssr)/./node_modules/pngjs/lib/chunkstream.js\");\nlet FilterAsync = __webpack_require__(/*! ./filter-parse-async */ \"(ssr)/./node_modules/pngjs/lib/filter-parse-async.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(ssr)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(ssr)/./node_modules/pngjs/lib/format-normaliser.js\");\n\nlet ParserAsync = (module.exports = function (options) {\n  ChunkStream.call(this);\n\n  this._parser = new Parser(options, {\n    read: this.read.bind(this),\n    error: this._handleError.bind(this),\n    metadata: this._handleMetaData.bind(this),\n    gamma: this.emit.bind(this, \"gamma\"),\n    palette: this._handlePalette.bind(this),\n    transColor: this._handleTransColor.bind(this),\n    finished: this._finished.bind(this),\n    inflateData: this._inflateData.bind(this),\n    simpleTransparency: this._simpleTransparency.bind(this),\n    headersFinished: this._headersFinished.bind(this),\n  });\n  this._options = options;\n  this.writable = true;\n\n  this._parser.start();\n});\nutil.inherits(ParserAsync, ChunkStream);\n\nParserAsync.prototype._handleError = function (err) {\n  this.emit(\"error\", err);\n\n  this.writable = false;\n\n  this.destroy();\n\n  if (this._inflate && this._inflate.destroy) {\n    this._inflate.destroy();\n  }\n\n  if (this._filter) {\n    this._filter.destroy();\n    // For backward compatibility with Node 7 and below.\n    // Suppress errors due to _inflate calling write() even after\n    // it's destroy()'ed.\n    this._filter.on(\"error\", function () {});\n  }\n\n  this.errord = true;\n};\n\nParserAsync.prototype._inflateData = function (data) {\n  if (!this._inflate) {\n    if (this._bitmapInfo.interlace) {\n      this._inflate = zlib.createInflate();\n\n      this._inflate.on(\"error\", this.emit.bind(this, \"error\"));\n      this._filter.on(\"complete\", this._complete.bind(this));\n\n      this._inflate.pipe(this._filter);\n    } else {\n      let rowSize =\n        ((this._bitmapInfo.width *\n          this._bitmapInfo.bpp *\n          this._bitmapInfo.depth +\n          7) >>\n          3) +\n        1;\n      let imageSize = rowSize * this._bitmapInfo.height;\n      let chunkSize = Math.max(imageSize, zlib.Z_MIN_CHUNK);\n\n      this._inflate = zlib.createInflate({ chunkSize: chunkSize });\n      let leftToInflate = imageSize;\n\n      let emitError = this.emit.bind(this, \"error\");\n      this._inflate.on(\"error\", function (err) {\n        if (!leftToInflate) {\n          return;\n        }\n\n        emitError(err);\n      });\n      this._filter.on(\"complete\", this._complete.bind(this));\n\n      let filterWrite = this._filter.write.bind(this._filter);\n      this._inflate.on(\"data\", function (chunk) {\n        if (!leftToInflate) {\n          return;\n        }\n\n        if (chunk.length > leftToInflate) {\n          chunk = chunk.slice(0, leftToInflate);\n        }\n\n        leftToInflate -= chunk.length;\n\n        filterWrite(chunk);\n      });\n\n      this._inflate.on(\"end\", this._filter.end.bind(this._filter));\n    }\n  }\n  this._inflate.write(data);\n};\n\nParserAsync.prototype._handleMetaData = function (metaData) {\n  this._metaData = metaData;\n  this._bitmapInfo = Object.create(metaData);\n\n  this._filter = new FilterAsync(this._bitmapInfo);\n};\n\nParserAsync.prototype._handleTransColor = function (transColor) {\n  this._bitmapInfo.transColor = transColor;\n};\n\nParserAsync.prototype._handlePalette = function (palette) {\n  this._bitmapInfo.palette = palette;\n};\n\nParserAsync.prototype._simpleTransparency = function () {\n  this._metaData.alpha = true;\n};\n\nParserAsync.prototype._headersFinished = function () {\n  // Up until this point, we don't know if we have a tRNS chunk (alpha)\n  // so we can't emit metadata any earlier\n  this.emit(\"metadata\", this._metaData);\n};\n\nParserAsync.prototype._finished = function () {\n  if (this.errord) {\n    return;\n  }\n\n  if (!this._inflate) {\n    this.emit(\"error\", \"No Inflate block\");\n  } else {\n    // no more data to inflate\n    this._inflate.end();\n  }\n};\n\nParserAsync.prototype._complete = function (filteredData) {\n  if (this.errord) {\n    return;\n  }\n\n  let normalisedBitmapData;\n\n  try {\n    let bitmapData = bitmapper.dataToBitMap(filteredData, this._bitmapInfo);\n\n    normalisedBitmapData = formatNormaliser(bitmapData, this._bitmapInfo);\n    bitmapData = null;\n  } catch (ex) {\n    this._handleError(ex);\n    return;\n  }\n\n  this.emit(\"parsed\", normalisedBitmapData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/parser-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet inflateSync = __webpack_require__(/*! ./sync-inflate */ \"(ssr)/./node_modules/pngjs/lib/sync-inflate.js\");\nif (!zlib.deflateSync) {\n  hasSyncZlib = false;\n}\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(ssr)/./node_modules/pngjs/lib/sync-reader.js\");\nlet FilterSync = __webpack_require__(/*! ./filter-parse-sync */ \"(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(ssr)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(ssr)/./node_modules/pngjs/lib/format-normaliser.js\");\n\nmodule.exports = function (buffer, options) {\n  if (!hasSyncZlib) {\n    throw new Error(\n      \"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\"\n    );\n  }\n\n  let err;\n  function handleError(_err_) {\n    err = _err_;\n  }\n\n  let metaData;\n  function handleMetaData(_metaData_) {\n    metaData = _metaData_;\n  }\n\n  function handleTransColor(transColor) {\n    metaData.transColor = transColor;\n  }\n\n  function handlePalette(palette) {\n    metaData.palette = palette;\n  }\n\n  function handleSimpleTransparency() {\n    metaData.alpha = true;\n  }\n\n  let gamma;\n  function handleGamma(_gamma_) {\n    gamma = _gamma_;\n  }\n\n  let inflateDataList = [];\n  function handleInflateData(inflatedData) {\n    inflateDataList.push(inflatedData);\n  }\n\n  let reader = new SyncReader(buffer);\n\n  let parser = new Parser(options, {\n    read: reader.read.bind(reader),\n    error: handleError,\n    metadata: handleMetaData,\n    gamma: handleGamma,\n    palette: handlePalette,\n    transColor: handleTransColor,\n    inflateData: handleInflateData,\n    simpleTransparency: handleSimpleTransparency,\n  });\n\n  parser.start();\n  reader.process();\n\n  if (err) {\n    throw err;\n  }\n\n  //join together the inflate datas\n  let inflateData = Buffer.concat(inflateDataList);\n  inflateDataList.length = 0;\n\n  let inflatedData;\n  if (metaData.interlace) {\n    inflatedData = zlib.inflateSync(inflateData);\n  } else {\n    let rowSize =\n      ((metaData.width * metaData.bpp * metaData.depth + 7) >> 3) + 1;\n    let imageSize = rowSize * metaData.height;\n    inflatedData = inflateSync(inflateData, {\n      chunkSize: imageSize,\n      maxLength: imageSize,\n    });\n  }\n  inflateData = null;\n\n  if (!inflatedData || !inflatedData.length) {\n    throw new Error(\"bad png - invalid inflate data response\");\n  }\n\n  let unfilteredData = FilterSync.process(inflatedData, metaData);\n  inflateData = null;\n\n  let bitmapData = bitmapper.dataToBitMap(unfilteredData, metaData);\n  unfilteredData = null;\n\n  let normalisedBitmapData = formatNormaliser(bitmapData, metaData);\n\n  metaData.data = normalisedBitmapData;\n  metaData.gamma = gamma || 0;\n\n  return metaData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/parser.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet CrcCalculator = __webpack_require__(/*! ./crc */ \"(ssr)/./node_modules/pngjs/lib/crc.js\");\n\nlet Parser = (module.exports = function (options, dependencies) {\n  this._options = options;\n  options.checkCRC = options.checkCRC !== false;\n\n  this._hasIHDR = false;\n  this._hasIEND = false;\n  this._emittedHeadersFinished = false;\n\n  // input flags/metadata\n  this._palette = [];\n  this._colorType = 0;\n\n  this._chunks = {};\n  this._chunks[constants.TYPE_IHDR] = this._handleIHDR.bind(this);\n  this._chunks[constants.TYPE_IEND] = this._handleIEND.bind(this);\n  this._chunks[constants.TYPE_IDAT] = this._handleIDAT.bind(this);\n  this._chunks[constants.TYPE_PLTE] = this._handlePLTE.bind(this);\n  this._chunks[constants.TYPE_tRNS] = this._handleTRNS.bind(this);\n  this._chunks[constants.TYPE_gAMA] = this._handleGAMA.bind(this);\n\n  this.read = dependencies.read;\n  this.error = dependencies.error;\n  this.metadata = dependencies.metadata;\n  this.gamma = dependencies.gamma;\n  this.transColor = dependencies.transColor;\n  this.palette = dependencies.palette;\n  this.parsed = dependencies.parsed;\n  this.inflateData = dependencies.inflateData;\n  this.finished = dependencies.finished;\n  this.simpleTransparency = dependencies.simpleTransparency;\n  this.headersFinished = dependencies.headersFinished || function () {};\n});\n\nParser.prototype.start = function () {\n  this.read(constants.PNG_SIGNATURE.length, this._parseSignature.bind(this));\n};\n\nParser.prototype._parseSignature = function (data) {\n  let signature = constants.PNG_SIGNATURE;\n\n  for (let i = 0; i < signature.length; i++) {\n    if (data[i] !== signature[i]) {\n      this.error(new Error(\"Invalid file signature\"));\n      return;\n    }\n  }\n  this.read(8, this._parseChunkBegin.bind(this));\n};\n\nParser.prototype._parseChunkBegin = function (data) {\n  // chunk content length\n  let length = data.readUInt32BE(0);\n\n  // chunk type\n  let type = data.readUInt32BE(4);\n  let name = \"\";\n  for (let i = 4; i < 8; i++) {\n    name += String.fromCharCode(data[i]);\n  }\n\n  //console.log('chunk ', name, length);\n\n  // chunk flags\n  let ancillary = Boolean(data[4] & 0x20); // or critical\n  //    priv = Boolean(data[5] & 0x20), // or public\n  //    safeToCopy = Boolean(data[7] & 0x20); // or unsafe\n\n  if (!this._hasIHDR && type !== constants.TYPE_IHDR) {\n    this.error(new Error(\"Expected IHDR on beggining\"));\n    return;\n  }\n\n  this._crc = new CrcCalculator();\n  this._crc.write(Buffer.from(name));\n\n  if (this._chunks[type]) {\n    return this._chunks[type](length);\n  }\n\n  if (!ancillary) {\n    this.error(new Error(\"Unsupported critical chunk type \" + name));\n    return;\n  }\n\n  this.read(length + 4, this._skipChunk.bind(this));\n};\n\nParser.prototype._skipChunk = function (/*data*/) {\n  this.read(8, this._parseChunkBegin.bind(this));\n};\n\nParser.prototype._handleChunkEnd = function () {\n  this.read(4, this._parseChunkEnd.bind(this));\n};\n\nParser.prototype._parseChunkEnd = function (data) {\n  let fileCrc = data.readInt32BE(0);\n  let calcCrc = this._crc.crc32();\n\n  // check CRC\n  if (this._options.checkCRC && calcCrc !== fileCrc) {\n    this.error(new Error(\"Crc error - \" + fileCrc + \" - \" + calcCrc));\n    return;\n  }\n\n  if (!this._hasIEND) {\n    this.read(8, this._parseChunkBegin.bind(this));\n  }\n};\n\nParser.prototype._handleIHDR = function (length) {\n  this.read(length, this._parseIHDR.bind(this));\n};\nParser.prototype._parseIHDR = function (data) {\n  this._crc.write(data);\n\n  let width = data.readUInt32BE(0);\n  let height = data.readUInt32BE(4);\n  let depth = data[8];\n  let colorType = data[9]; // bits: 1 palette, 2 color, 4 alpha\n  let compr = data[10];\n  let filter = data[11];\n  let interlace = data[12];\n\n  // console.log('    width', width, 'height', height,\n  //     'depth', depth, 'colorType', colorType,\n  //     'compr', compr, 'filter', filter, 'interlace', interlace\n  // );\n\n  if (\n    depth !== 8 &&\n    depth !== 4 &&\n    depth !== 2 &&\n    depth !== 1 &&\n    depth !== 16\n  ) {\n    this.error(new Error(\"Unsupported bit depth \" + depth));\n    return;\n  }\n  if (!(colorType in constants.COLORTYPE_TO_BPP_MAP)) {\n    this.error(new Error(\"Unsupported color type\"));\n    return;\n  }\n  if (compr !== 0) {\n    this.error(new Error(\"Unsupported compression method\"));\n    return;\n  }\n  if (filter !== 0) {\n    this.error(new Error(\"Unsupported filter method\"));\n    return;\n  }\n  if (interlace !== 0 && interlace !== 1) {\n    this.error(new Error(\"Unsupported interlace method\"));\n    return;\n  }\n\n  this._colorType = colorType;\n\n  let bpp = constants.COLORTYPE_TO_BPP_MAP[this._colorType];\n\n  this._hasIHDR = true;\n\n  this.metadata({\n    width: width,\n    height: height,\n    depth: depth,\n    interlace: Boolean(interlace),\n    palette: Boolean(colorType & constants.COLORTYPE_PALETTE),\n    color: Boolean(colorType & constants.COLORTYPE_COLOR),\n    alpha: Boolean(colorType & constants.COLORTYPE_ALPHA),\n    bpp: bpp,\n    colorType: colorType,\n  });\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handlePLTE = function (length) {\n  this.read(length, this._parsePLTE.bind(this));\n};\nParser.prototype._parsePLTE = function (data) {\n  this._crc.write(data);\n\n  let entries = Math.floor(data.length / 3);\n  // console.log('Palette:', entries);\n\n  for (let i = 0; i < entries; i++) {\n    this._palette.push([data[i * 3], data[i * 3 + 1], data[i * 3 + 2], 0xff]);\n  }\n\n  this.palette(this._palette);\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleTRNS = function (length) {\n  this.simpleTransparency();\n  this.read(length, this._parseTRNS.bind(this));\n};\nParser.prototype._parseTRNS = function (data) {\n  this._crc.write(data);\n\n  // palette\n  if (this._colorType === constants.COLORTYPE_PALETTE_COLOR) {\n    if (this._palette.length === 0) {\n      this.error(new Error(\"Transparency chunk must be after palette\"));\n      return;\n    }\n    if (data.length > this._palette.length) {\n      this.error(new Error(\"More transparent colors than palette size\"));\n      return;\n    }\n    for (let i = 0; i < data.length; i++) {\n      this._palette[i][3] = data[i];\n    }\n    this.palette(this._palette);\n  }\n\n  // for colorType 0 (grayscale) and 2 (rgb)\n  // there might be one gray/color defined as transparent\n  if (this._colorType === constants.COLORTYPE_GRAYSCALE) {\n    // grey, 2 bytes\n    this.transColor([data.readUInt16BE(0)]);\n  }\n  if (this._colorType === constants.COLORTYPE_COLOR) {\n    this.transColor([\n      data.readUInt16BE(0),\n      data.readUInt16BE(2),\n      data.readUInt16BE(4),\n    ]);\n  }\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleGAMA = function (length) {\n  this.read(length, this._parseGAMA.bind(this));\n};\nParser.prototype._parseGAMA = function (data) {\n  this._crc.write(data);\n  this.gamma(data.readUInt32BE(0) / constants.GAMMA_DIVISION);\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleIDAT = function (length) {\n  if (!this._emittedHeadersFinished) {\n    this._emittedHeadersFinished = true;\n    this.headersFinished();\n  }\n  this.read(-length, this._parseIDAT.bind(this, length));\n};\nParser.prototype._parseIDAT = function (length, data) {\n  this._crc.write(data);\n\n  if (\n    this._colorType === constants.COLORTYPE_PALETTE_COLOR &&\n    this._palette.length === 0\n  ) {\n    throw new Error(\"Expected palette not found\");\n  }\n\n  this.inflateData(data);\n  let leftOverLength = length - data.length;\n\n  if (leftOverLength > 0) {\n    this._handleIDAT(leftOverLength);\n  } else {\n    this._handleChunkEnd();\n  }\n};\n\nParser.prototype._handleIEND = function (length) {\n  this.read(length, this._parseIEND.bind(this));\n};\nParser.prototype._parseIEND = function (data) {\n  this._crc.write(data);\n\n  this._hasIEND = true;\n  this._handleChunkEnd();\n\n  if (this.finished) {\n    this.finished();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/png-sync.js":
/*!********************************************!*\
  !*** ./node_modules/pngjs/lib/png-sync.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet parse = __webpack_require__(/*! ./parser-sync */ \"(ssr)/./node_modules/pngjs/lib/parser-sync.js\");\nlet pack = __webpack_require__(/*! ./packer-sync */ \"(ssr)/./node_modules/pngjs/lib/packer-sync.js\");\n\nexports.read = function (buffer, options) {\n  return parse(buffer, options || {});\n};\n\nexports.write = function (png, options) {\n  return pack(png, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BuZy1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFlBQVksbUJBQU8sQ0FBQyxvRUFBZTtBQUNuQyxXQUFXLG1CQUFPLENBQUMsb0VBQWU7O0FBRWxDLFlBQVk7QUFDWixvQ0FBb0M7QUFDcEM7O0FBRUEsYUFBYTtBQUNiO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxidGwtbGFwLXRyaW5oLWRpLWRvbmdcXGFwcHNcXGFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxwbmdqc1xcbGliXFxwbmctc3luYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubGV0IHBhcnNlID0gcmVxdWlyZShcIi4vcGFyc2VyLXN5bmNcIik7XG5sZXQgcGFjayA9IHJlcXVpcmUoXCIuL3BhY2tlci1zeW5jXCIpO1xuXG5leHBvcnRzLnJlYWQgPSBmdW5jdGlvbiAoYnVmZmVyLCBvcHRpb25zKSB7XG4gIHJldHVybiBwYXJzZShidWZmZXIsIG9wdGlvbnMgfHwge30pO1xufTtcblxuZXhwb3J0cy53cml0ZSA9IGZ1bmN0aW9uIChwbmcsIG9wdGlvbnMpIHtcbiAgcmV0dXJuIHBhY2socG5nLCBvcHRpb25zKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/png-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/png.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/png.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet Parser = __webpack_require__(/*! ./parser-async */ \"(ssr)/./node_modules/pngjs/lib/parser-async.js\");\nlet Packer = __webpack_require__(/*! ./packer-async */ \"(ssr)/./node_modules/pngjs/lib/packer-async.js\");\nlet PNGSync = __webpack_require__(/*! ./png-sync */ \"(ssr)/./node_modules/pngjs/lib/png-sync.js\");\n\nlet PNG = (exports.PNG = function (options) {\n  Stream.call(this);\n\n  options = options || {}; // eslint-disable-line no-param-reassign\n\n  // coerce pixel dimensions to integers (also coerces undefined -> 0):\n  this.width = options.width | 0;\n  this.height = options.height | 0;\n\n  this.data =\n    this.width > 0 && this.height > 0\n      ? Buffer.alloc(4 * this.width * this.height)\n      : null;\n\n  if (options.fill && this.data) {\n    this.data.fill(0);\n  }\n\n  this.gamma = 0;\n  this.readable = this.writable = true;\n\n  this._parser = new Parser(options);\n\n  this._parser.on(\"error\", this.emit.bind(this, \"error\"));\n  this._parser.on(\"close\", this._handleClose.bind(this));\n  this._parser.on(\"metadata\", this._metadata.bind(this));\n  this._parser.on(\"gamma\", this._gamma.bind(this));\n  this._parser.on(\n    \"parsed\",\n    function (data) {\n      this.data = data;\n      this.emit(\"parsed\", data);\n    }.bind(this)\n  );\n\n  this._packer = new Packer(options);\n  this._packer.on(\"data\", this.emit.bind(this, \"data\"));\n  this._packer.on(\"end\", this.emit.bind(this, \"end\"));\n  this._parser.on(\"close\", this._handleClose.bind(this));\n  this._packer.on(\"error\", this.emit.bind(this, \"error\"));\n});\nutil.inherits(PNG, Stream);\n\nPNG.sync = PNGSync;\n\nPNG.prototype.pack = function () {\n  if (!this.data || !this.data.length) {\n    this.emit(\"error\", \"No data provided\");\n    return this;\n  }\n\n  process.nextTick(\n    function () {\n      this._packer.pack(this.data, this.width, this.height, this.gamma);\n    }.bind(this)\n  );\n\n  return this;\n};\n\nPNG.prototype.parse = function (data, callback) {\n  if (callback) {\n    let onParsed, onError;\n\n    onParsed = function (parsedData) {\n      this.removeListener(\"error\", onError);\n\n      this.data = parsedData;\n      callback(null, this);\n    }.bind(this);\n\n    onError = function (err) {\n      this.removeListener(\"parsed\", onParsed);\n\n      callback(err, null);\n    }.bind(this);\n\n    this.once(\"parsed\", onParsed);\n    this.once(\"error\", onError);\n  }\n\n  this.end(data);\n  return this;\n};\n\nPNG.prototype.write = function (data) {\n  this._parser.write(data);\n  return true;\n};\n\nPNG.prototype.end = function (data) {\n  this._parser.end(data);\n};\n\nPNG.prototype._metadata = function (metadata) {\n  this.width = metadata.width;\n  this.height = metadata.height;\n\n  this.emit(\"metadata\", metadata);\n};\n\nPNG.prototype._gamma = function (gamma) {\n  this.gamma = gamma;\n};\n\nPNG.prototype._handleClose = function () {\n  if (!this._parser.writable && !this._packer.readable) {\n    this.emit(\"close\");\n  }\n};\n\nPNG.bitblt = function (src, dst, srcX, srcY, width, height, deltaX, deltaY) {\n  // eslint-disable-line max-params\n  // coerce pixel dimensions to integers (also coerces undefined -> 0):\n  /* eslint-disable no-param-reassign */\n  srcX |= 0;\n  srcY |= 0;\n  width |= 0;\n  height |= 0;\n  deltaX |= 0;\n  deltaY |= 0;\n  /* eslint-enable no-param-reassign */\n\n  if (\n    srcX > src.width ||\n    srcY > src.height ||\n    srcX + width > src.width ||\n    srcY + height > src.height\n  ) {\n    throw new Error(\"bitblt reading outside image\");\n  }\n\n  if (\n    deltaX > dst.width ||\n    deltaY > dst.height ||\n    deltaX + width > dst.width ||\n    deltaY + height > dst.height\n  ) {\n    throw new Error(\"bitblt writing outside image\");\n  }\n\n  for (let y = 0; y < height; y++) {\n    src.data.copy(\n      dst.data,\n      ((deltaY + y) * dst.width + deltaX) << 2,\n      ((srcY + y) * src.width + srcX) << 2,\n      ((srcY + y) * src.width + srcX + width) << 2\n    );\n  }\n};\n\nPNG.prototype.bitblt = function (\n  dst,\n  srcX,\n  srcY,\n  width,\n  height,\n  deltaX,\n  deltaY\n) {\n  // eslint-disable-line max-params\n\n  PNG.bitblt(this, dst, srcX, srcY, width, height, deltaX, deltaY);\n  return this;\n};\n\nPNG.adjustGamma = function (src) {\n  if (src.gamma) {\n    for (let y = 0; y < src.height; y++) {\n      for (let x = 0; x < src.width; x++) {\n        let idx = (src.width * y + x) << 2;\n\n        for (let i = 0; i < 3; i++) {\n          let sample = src.data[idx + i] / 255;\n          sample = Math.pow(sample, 1 / 2.2 / src.gamma);\n          src.data[idx + i] = Math.round(sample * 255);\n        }\n      }\n    }\n    src.gamma = 0;\n  }\n};\n\nPNG.prototype.adjustGamma = function () {\n  PNG.adjustGamma(this);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/png.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/sync-inflate.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/sync-inflate.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\n\nlet assert = (__webpack_require__(/*! assert */ \"assert\").ok);\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet util = __webpack_require__(/*! util */ \"util\");\n\nlet kMaxLength = (__webpack_require__(/*! buffer */ \"buffer\").kMaxLength);\n\nfunction Inflate(opts) {\n  if (!(this instanceof Inflate)) {\n    return new Inflate(opts);\n  }\n\n  if (opts && opts.chunkSize < zlib.Z_MIN_CHUNK) {\n    opts.chunkSize = zlib.Z_MIN_CHUNK;\n  }\n\n  zlib.Inflate.call(this, opts);\n\n  // Node 8 --> 9 compatibility check\n  this._offset = this._offset === undefined ? this._outOffset : this._offset;\n  this._buffer = this._buffer || this._outBuffer;\n\n  if (opts && opts.maxLength != null) {\n    this._maxLength = opts.maxLength;\n  }\n}\n\nfunction createInflate(opts) {\n  return new Inflate(opts);\n}\n\nfunction _close(engine, callback) {\n  if (callback) {\n    process.nextTick(callback);\n  }\n\n  // Caller may invoke .close after a zlib error (which will null _handle).\n  if (!engine._handle) {\n    return;\n  }\n\n  engine._handle.close();\n  engine._handle = null;\n}\n\nInflate.prototype._processChunk = function (chunk, flushFlag, asyncCb) {\n  if (typeof asyncCb === \"function\") {\n    return zlib.Inflate._processChunk.call(this, chunk, flushFlag, asyncCb);\n  }\n\n  let self = this;\n\n  let availInBefore = chunk && chunk.length;\n  let availOutBefore = this._chunkSize - this._offset;\n  let leftToInflate = this._maxLength;\n  let inOff = 0;\n\n  let buffers = [];\n  let nread = 0;\n\n  let error;\n  this.on(\"error\", function (err) {\n    error = err;\n  });\n\n  function handleChunk(availInAfter, availOutAfter) {\n    if (self._hadError) {\n      return;\n    }\n\n    let have = availOutBefore - availOutAfter;\n    assert(have >= 0, \"have should not go down\");\n\n    if (have > 0) {\n      let out = self._buffer.slice(self._offset, self._offset + have);\n      self._offset += have;\n\n      if (out.length > leftToInflate) {\n        out = out.slice(0, leftToInflate);\n      }\n\n      buffers.push(out);\n      nread += out.length;\n      leftToInflate -= out.length;\n\n      if (leftToInflate === 0) {\n        return false;\n      }\n    }\n\n    if (availOutAfter === 0 || self._offset >= self._chunkSize) {\n      availOutBefore = self._chunkSize;\n      self._offset = 0;\n      self._buffer = Buffer.allocUnsafe(self._chunkSize);\n    }\n\n    if (availOutAfter === 0) {\n      inOff += availInBefore - availInAfter;\n      availInBefore = availInAfter;\n\n      return true;\n    }\n\n    return false;\n  }\n\n  assert(this._handle, \"zlib binding closed\");\n  let res;\n  do {\n    res = this._handle.writeSync(\n      flushFlag,\n      chunk, // in\n      inOff, // in_off\n      availInBefore, // in_len\n      this._buffer, // out\n      this._offset, //out_off\n      availOutBefore\n    ); // out_len\n    // Node 8 --> 9 compatibility check\n    res = res || this._writeState;\n  } while (!this._hadError && handleChunk(res[0], res[1]));\n\n  if (this._hadError) {\n    throw error;\n  }\n\n  if (nread >= kMaxLength) {\n    _close(this);\n    throw new RangeError(\n      \"Cannot create final Buffer. It would be larger than 0x\" +\n        kMaxLength.toString(16) +\n        \" bytes\"\n    );\n  }\n\n  let buf = Buffer.concat(buffers, nread);\n  _close(this);\n\n  return buf;\n};\n\nutil.inherits(Inflate, zlib.Inflate);\n\nfunction zlibBufferSync(engine, buffer) {\n  if (typeof buffer === \"string\") {\n    buffer = Buffer.from(buffer);\n  }\n  if (!(buffer instanceof Buffer)) {\n    throw new TypeError(\"Not a string or buffer\");\n  }\n\n  let flushFlag = engine._finishFlushFlag;\n  if (flushFlag == null) {\n    flushFlag = zlib.Z_FINISH;\n  }\n\n  return engine._processChunk(buffer, flushFlag);\n}\n\nfunction inflateSync(buffer, opts) {\n  return zlibBufferSync(new Inflate(opts), buffer);\n}\n\nmodule.exports = exports = inflateSync;\nexports.Inflate = Inflate;\nexports.createInflate = createInflate;\nexports.inflateSync = inflateSync;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/sync-inflate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/sync-reader.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/sync-reader.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\n\nlet SyncReader = (module.exports = function (buffer) {\n  this._buffer = buffer;\n  this._reads = [];\n});\n\nSyncReader.prototype.read = function (length, callback) {\n  this._reads.push({\n    length: Math.abs(length), // if length < 0 then at most this length\n    allowLess: length < 0,\n    func: callback,\n  });\n};\n\nSyncReader.prototype.process = function () {\n  // as long as there is any data and read requests\n  while (this._reads.length > 0 && this._buffer.length) {\n    let read = this._reads[0];\n\n    if (\n      this._buffer.length &&\n      (this._buffer.length >= read.length || read.allowLess)\n    ) {\n      // ok there is any data so that we can satisfy this request\n      this._reads.shift(); // == read\n\n      let buf = this._buffer;\n\n      this._buffer = buf.slice(read.length);\n\n      read.func.call(this, buf.slice(0, read.length));\n    } else {\n      break;\n    }\n  }\n\n  if (this._reads.length > 0) {\n    return new Error(\"There are some read requests waitng on finished stream\");\n  }\n\n  if (this._buffer.length > 0) {\n    return new Error(\"unrecognised content at end of stream\");\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3N5bmMtcmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCOztBQUUzQjs7QUFFQTs7QUFFQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcYnRsLWxhcC10cmluaC1kaS1kb25nXFxhcHBzXFxhZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccG5nanNcXGxpYlxcc3luYy1yZWFkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmxldCBTeW5jUmVhZGVyID0gKG1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKGJ1ZmZlcikge1xuICB0aGlzLl9idWZmZXIgPSBidWZmZXI7XG4gIHRoaXMuX3JlYWRzID0gW107XG59KTtcblxuU3luY1JlYWRlci5wcm90b3R5cGUucmVhZCA9IGZ1bmN0aW9uIChsZW5ndGgsIGNhbGxiYWNrKSB7XG4gIHRoaXMuX3JlYWRzLnB1c2goe1xuICAgIGxlbmd0aDogTWF0aC5hYnMobGVuZ3RoKSwgLy8gaWYgbGVuZ3RoIDwgMCB0aGVuIGF0IG1vc3QgdGhpcyBsZW5ndGhcbiAgICBhbGxvd0xlc3M6IGxlbmd0aCA8IDAsXG4gICAgZnVuYzogY2FsbGJhY2ssXG4gIH0pO1xufTtcblxuU3luY1JlYWRlci5wcm90b3R5cGUucHJvY2VzcyA9IGZ1bmN0aW9uICgpIHtcbiAgLy8gYXMgbG9uZyBhcyB0aGVyZSBpcyBhbnkgZGF0YSBhbmQgcmVhZCByZXF1ZXN0c1xuICB3aGlsZSAodGhpcy5fcmVhZHMubGVuZ3RoID4gMCAmJiB0aGlzLl9idWZmZXIubGVuZ3RoKSB7XG4gICAgbGV0IHJlYWQgPSB0aGlzLl9yZWFkc1swXTtcblxuICAgIGlmIChcbiAgICAgIHRoaXMuX2J1ZmZlci5sZW5ndGggJiZcbiAgICAgICh0aGlzLl9idWZmZXIubGVuZ3RoID49IHJlYWQubGVuZ3RoIHx8IHJlYWQuYWxsb3dMZXNzKVxuICAgICkge1xuICAgICAgLy8gb2sgdGhlcmUgaXMgYW55IGRhdGEgc28gdGhhdCB3ZSBjYW4gc2F0aXNmeSB0aGlzIHJlcXVlc3RcbiAgICAgIHRoaXMuX3JlYWRzLnNoaWZ0KCk7IC8vID09IHJlYWRcblxuICAgICAgbGV0IGJ1ZiA9IHRoaXMuX2J1ZmZlcjtcblxuICAgICAgdGhpcy5fYnVmZmVyID0gYnVmLnNsaWNlKHJlYWQubGVuZ3RoKTtcblxuICAgICAgcmVhZC5mdW5jLmNhbGwodGhpcywgYnVmLnNsaWNlKDAsIHJlYWQubGVuZ3RoKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgfVxuXG4gIGlmICh0aGlzLl9yZWFkcy5sZW5ndGggPiAwKSB7XG4gICAgcmV0dXJuIG5ldyBFcnJvcihcIlRoZXJlIGFyZSBzb21lIHJlYWQgcmVxdWVzdHMgd2FpdG5nIG9uIGZpbmlzaGVkIHN0cmVhbVwiKTtcbiAgfVxuXG4gIGlmICh0aGlzLl9idWZmZXIubGVuZ3RoID4gMCkge1xuICAgIHJldHVybiBuZXcgRXJyb3IoXCJ1bnJlY29nbmlzZWQgY29udGVudCBhdCBlbmQgb2Ygc3RyZWFtXCIpO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/sync-reader.js\n");

/***/ })

};
;
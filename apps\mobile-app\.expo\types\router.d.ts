/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in` | `/check-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/ScanQRCode` | `/check-in/ScanQRCode`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/session-detail` | `/check-in/session-detail`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/add-order` | `/orders/add-order`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/edit-order` | `/orders/edit-order`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/payment` | `/orders/payment`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/types` | `/orders/types`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tables` | `/tables`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/check-in` | `/check-in`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/check-in/ScanQRCode` | `/check-in/ScanQRCode`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/check-in/session-detail` | `/check-in/session-detail`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders/add-order` | `/orders/add-order`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders/edit-order` | `/orders/edit-order`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders/payment` | `/orders/payment`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders/types` | `/orders/types`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/tables` | `/tables`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/login${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/register${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/check-in${`?${string}` | `#${string}` | ''}` | `/check-in${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/check-in/ScanQRCode${`?${string}` | `#${string}` | ''}` | `/check-in/ScanQRCode${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/check-in/session-detail${`?${string}` | `#${string}` | ''}` | `/check-in/session-detail${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/notifications${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders/add-order${`?${string}` | `#${string}` | ''}` | `/orders/add-order${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders/edit-order${`?${string}` | `#${string}` | ''}` | `/orders/edit-order${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders${`?${string}` | `#${string}` | ''}` | `/orders${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders/payment${`?${string}` | `#${string}` | ''}` | `/orders/payment${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders/types${`?${string}` | `#${string}` | ''}` | `/orders/types${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/tables${`?${string}` | `#${string}` | ''}` | `/tables${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in` | `/check-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/ScanQRCode` | `/check-in/ScanQRCode`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/check-in/session-detail` | `/check-in/session-detail`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/add-order` | `/orders/add-order`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/edit-order` | `/orders/edit-order`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/payment` | `/orders/payment`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders/types` | `/orders/types`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tables` | `/tables`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
